package com.mall.project.service.quantifyCount;

import com.mall.common.api.CommonPage;

import java.util.List;
import java.util.Map;

/**
 * 量化数计算服务接口
 */
public interface QuantifyCountService {

    /**
     * 量化数设置查询
     */
    public Map<String, Object> getQuantifyCount();

    /**
     * 每天的量化数计算
     */
    public void updateQuantifyCount();

    /**
     * 量化数设置
     */
    public Map<String, Object> saveOrUpdateQuantifyCount(String isEnabled,String proportion,Integer updatePerson);

    /**
     *  查询量化数设置 分页显示
     */
    public CommonPage<Map<String, Object>> queryQuantifyCountPages(String phone, String startTime, String endTime, int pageNum, int pageSize);

    /**
     * 查看 所有合作企业Admain的各ID每日每笔数据量化数比 得出 Admin的每日累计量化数
     */
    public String getAdminDailyQuantity(String startTime);

    /**
     * 中南惠C的每日所有ID累计量化数
     */
    public String cweightCountTotal(String startTime);

    /**
     * 中南惠B的每日所有ID累计量化数
     */
    public String bweightCountTotal(String startTime);

    /**
     * 中南惠系统的每日总累计量化数 = Admin的每日累计量化数 + 中南惠C的每日所有ID累计量化数 + 中南惠所有B的每日累计量化数
     */
    public String sumWeightCountTotal(String startTime);

    /**
     *  量化数设置 导出 Excel
     */
    public List<Map<String, Object>> exportQuantifyCountExcel(String phone, String startTime, String endTime);

    /**
     * 今日总量化数
     */
    public String todayTotalQuan(String phone, String startTime);
    /**
     * 今日累计量化数
     */
    public String weightCountTotal(String phone, String startTime);

    /**
     * Admin的累计量化数
     */
    public String adminTotalQuantity(String startTime);

    /**
     * 计算 中南惠C的所有ID累计量化数
     */
    public String cweightCountTotalAllDays(String startTime);

    /**
     * 计算 中南惠B的所有ID累计量化数
     */
    public String bweightCountTotalAlldays(String startTime);

    /**
     * 计算中南惠系统的总累计量化数 = Admin的累计量化数 + 中南惠C的所有ID累计量化数 + 中南惠B的所有ID累计量化数
     */
    public String sumWeightCountTotalAllDays(String startTime);

    /**
     * 今日总分量
     */
    public String todayTotalWeight(String phone, String startTime);

    /**
     * 今日累计总分量
     */
    public String totalWeight(String phone, String startTime);

    /**
     * 更新 累计量化数
     */
    public void updateTotalQuantifyCount();
}
