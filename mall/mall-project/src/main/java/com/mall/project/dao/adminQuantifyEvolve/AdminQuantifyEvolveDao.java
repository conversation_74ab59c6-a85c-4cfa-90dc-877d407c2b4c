package com.mall.project.dao.adminQuantifyEvolve;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Admin量化值进化量 数据访问接口
 */
@Repository
@Slf4j
public class AdminQuantifyEvolveDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Admin量化值进化量更新或保存
     */
    public void saveOrUpdateAdminQuantifyEvolve(String phone, String value, String updateDate) {
        // 先查询是否存在相同phone和date的记录
        /*String checkSql = "SELECT EXISTS(SELECT 1 FROM admin_Quantify_Evolve WHERE phone = ? AND update_date = CURDATE())";
        Boolean exists = jdbcTemplate.queryForObject(checkSql, Boolean.class, phone);
        
        if (exists) {
            // 如果存在，则更新
            String updateSql = "UPDATE admin_Quantify_Evolve SET value = ? WHERE phone = ? AND update_date = CURDATE()";
            jdbcTemplate.update(updateSql, value, phone);
        } else {
            // 如果不存在，则插入
            String insertSql = "INSERT INTO admin_Quantify_Evolve (phone, value, update_date) VALUES (?, ?, CURDATE())";
            jdbcTemplate.update(insertSql, phone, value);
        }*/

        //使用 SELECT EXISTS 判断量化值进化量表中是否已经存在 CURDATE() - INTERVAL 1 DAY 的数据 如果存在则更新,不存在则插入
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM quantify_evolve WHERE phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, phone) == 0) {
            String sql = "INSERT INTO quantify_evolve(phone,quantify_evolve,quantify_evolve_total,update_date)VALUES(?,?,?,CURDATE() )";
            jdbcTemplate.update(sql, phone, value, new BigDecimal(value).add(new BigDecimal(sumQuantifyEvolve(phone))).setScale(2, BigDecimal.ROUND_DOWN));
        }else{
            String sql = "UPDATE quantify_evolve SET quantify_evolve = ?,quantify_evolve_total = ? WHERE phone = ? AND DATE(update_date) = CURDATE() ";
            jdbcTemplate.update(sql, value, new BigDecimal(value).add(new BigDecimal(sumQuantifyEvolve(phone))).setScale(2, BigDecimal.ROUND_DOWN), phone);
        }
    }

    /**
     * 统计 quantify_evolve ,phone 的和
     */
    public String sumQuantifyEvolve(String phone) {
        try{
            String sql = "SELECT SUM(quantify_evolve) FROM quantify_evolve WHERE phone = ? AND DATE(update_date) < CURDATE() ";
            String sum = jdbcTemplate.queryForObject(sql, String.class, phone);
            return sum == null ? "0" : sum;
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 今日Admin累计量化值进化量
     */
    public String todayTotalAdminQuantifyEvolve(String phone,String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COALESCE(SUM(value),0) as value FROM admin_Quantify_Evolve WHERE 1=1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND update_date = ?";
            params.add(startTime);
        }else{
            sql += " AND update_date = CURDATE() ";
        }
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, String.class);
        }else{
            return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
        }
    }

    /**
     * Admin累计量化值进化量
     */
    public String totalAdminQuantifyEvolve(String phone,String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COALESCE(SUM(value),0) as value FROM admin_Quantify_Evolve WHERE 1=1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND update_date <= ?";
            params.add(startTime);
        }else{
            sql += " AND update_date <= CURDATE() ";
        }
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, String.class);
        }else{
            return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
        }
    }

    /**
     * 查询Admin量化值, 分页显示
     */
    public List<Map<String, Object>> queryAdminAdminQuantifyEvolvePages(String phone, String startDate, String endDate, int limit, int offset) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT phone, value, update_date FROM admin_Quantify_Evolve WHERE 1=1");
        
        List<Object> params = new ArrayList<>();
        
        if (phone != null && !phone.trim().isEmpty()) {
            sql.append(" AND phone like ?");
            params.add("%" + phone + "%");
        }
        
        if (startDate != null && !startDate.trim().isEmpty()) {
            sql.append(" AND update_date = ?");
            params.add(startDate);
        }else{
            sql.append(" AND update_date <= CURDATE() ");
        }
        
        sql.append(" ORDER BY update_date DESC,id DESC LIMIT ? OFFSET ?");
        params.add(limit);
        params.add(offset);
        
        return jdbcTemplate.queryForList(sql.toString(), params.toArray());
    }

    /**
     * 查询Admin量化值总数，用于分页
     */
    public long totalAdminQuantifyEvolveCount(String phone, String startDate, String endDate) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*) FROM admin_Quantify_Evolve WHERE 1=1");
        
        List<Object> params = new ArrayList<>();
        
        if (phone != null && !phone.trim().isEmpty()) {
            sql.append(" AND phone like ?");
            params.add("%" + phone + "%");
        }
        
        if (startDate != null && !startDate.trim().isEmpty()) {
            sql.append(" AND update_date = ?");
            params.add(startDate);
        }else{
            sql.append(" AND update_date <= CURDATE() ");
        }
        
        Long count = jdbcTemplate.queryForObject(sql.toString(), Long.class, params.toArray());
        return count != null ? count : 0L;
    }

    /**
     * 导出Admin量化值进化量 Excel
     */
    public List<Map<String, Object>> exportAdminAdminQuantifyEvolveExcel(String phone,String startDate, String endDate) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT phone, value, update_date FROM admin_Quantify_Evolve WHERE 1=1");
        
        List<Object> params = new ArrayList<>();

        if (phone != null && !phone.trim().isEmpty()) {
            sql.append(" AND phone like ?");
            params.add("%" + phone + "%");
        }
        
        if (startDate != null && !startDate.trim().isEmpty()) {
            sql.append(" AND update_date = ?");
            params.add(startDate);
        }else{
            sql.append(" AND update_date <= CURDATE() ");
        }
        
        sql.append(" ORDER BY update_date DESC");
        
        return jdbcTemplate.queryForList(sql.toString(), params.toArray());
    }

}