package com.mall.project.service.functionDatas;

import com.mall.common.api.CommonPage;
import com.mall.project.dto.functionDatas.FunctionDatas;

import java.util.List;
import java.util.Map;

public interface FunctionDatasService {

    /**
     * 添加功能数据
     */
    public int addFunctionDatas(FunctionDatas pojo, int updatePerson);

    /**
     * 查询功能数据,分页显示
     */
    public CommonPage<Map<String, Object>> queryFunctionDatasPages(String phone, String startDate, String endDate, int pageNum, int pageSize);

    /**
     * 导出功能数据 Excel
     */
    public List<Map<String, Object>> exportFunctionDatasExcel(String phone, String startDate, String endDate);

    /**
     * 今日总功能数值
     */
    public String todayTotalFunctionDatas(String phone, String startDate);

    /**
     * 今日总量化值
     */
    public String todayTotalQuantify(String phone, String startDate);

    /**
     * 今日总补贴金
     */
    public String todayTotalSubsidy(String phone, String startDate);

    /**
     * 今日总累计功能数值
     */
    public String todayAllTotalFunctionDatas(String phone, String startDate);

    /**
     * 今日总累计量化值
     */
    public String todayAllTotalQuantify(String phone, String startDate);

    /**
     * 今日总累计补贴金
     */
    public String todayAllTotalSubsidy(String phone, String startDate);

    /**
     *  将功能数据的 功能数值 更新到 量化数,这里由定时任务触发,一般在凌晨1点执行
     */
    public void updateQuantifyCount();

    /**
     * 将功能数据的 量化值 更新到 量化值,这里由定时任务触发,一般在凌晨1点执行
     */
    public void updateQuantizationValue();
}
