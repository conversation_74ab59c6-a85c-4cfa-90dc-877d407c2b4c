package com.mall.project.controller.quantifyEvolve;

import com.mall.common.api.CommonPage;
import com.mall.common.api.CommonResult;
import com.mall.common.util.UniversalExcelExporter;
import com.mall.project.dto.quantifyEvolve.QuantifyEvolve;
import com.mall.project.service.quantifyEvolve.QuantifyEvolveService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 量化值进化量控制器
 */
@RestController
@RequestMapping("/api")
@Slf4j
public class QuantifyEvolveController {

    @Autowired
    private QuantifyEvolveService quantifyEvolveService;

    /**
     * 查询量化值进化量, 分页显示
     */
    @PostMapping("/queryQuantifyEvolvePages")
    public CommonResult<CommonPage<Map<String, Object>>> queryQuantifyEvolvePages(@RequestBody @Valid QuantifyEvolve param) {
        CommonPage<Map<String, Object>> commonPage = quantifyEvolveService.queryQuantifyEvolvePages(param.getPhone(), param.getStartDate(), param.getEndDate(), param.getPageNum(), param.getPageSize());
        return CommonResult.success(commonPage);
    }

    /**
     * 量化值进化量, 导出 Excel
     */
    @PostMapping("/exportQuantifyEvolveExcel")
    public void exportQuantifyEvolveExcel(HttpServletResponse response,@RequestBody @Valid QuantifyEvolve param) {
        try {
            // 获取量化值进化量数据
            List<Map<String, Object>> dataList = quantifyEvolveService.exportQuantifyEvolveExcel(param.getPhone(), param.getStartDate(), param.getEndDate());

            // 获取汇总数据
            String todayTotalQuantifyEvolve = quantifyEvolveService.todayTotalQuantifyEvolve(param.getPhone(), param.getStartDate());  //今日总量化值进化量
            String totalQuantifyEvolve = quantifyEvolveService.totalQuantifyEvolve(param.getPhone(), param.getStartDate());            //累计量化值进化量
            String todayAdminQuantifyEvolve = quantifyEvolveService.todayAdminQuantifyEvolve(param.getStartDate());                      //Admin今日量化值进化量
            String totalAdminQuantifyEvolve = quantifyEvolveService.totalAdminQuantifyEvolve(param.getStartDate());                      //Admin累计量化值进化量

            // 配置字段映射
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("updateDate", "updateDate");
            fieldMapping.put("phone", "phone");
            fieldMapping.put("userName", "username");
            fieldMapping.put("quantifyEvolve", "quantifyEvolve");
            fieldMapping.put("quantifyEvolveTotal", "quantifyEvolveTotal");

            // 配置汇总信息（一行显示两个统计项）
            List<List<UniversalExcelExporter.SummaryItem>> summaryRows = Arrays.asList(
                Arrays.asList(
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("今日量化值进化量")
                        .value(todayTotalQuantifyEvolve)
                        .build(),
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("累计量化值进化量")
                        .value(totalQuantifyEvolve)
                        .build(),
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("Admin今日量化值进化量")
                        .value(todayAdminQuantifyEvolve)
                        .build(),
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("Admin累计量化值进化量")
                        .value(totalAdminQuantifyEvolve)
                        .build()
                )
            );

            // 构建导出配置
            UniversalExcelExporter.ExportConfig<QuantifyEvolve> config = UniversalExcelExporter.ExportConfig.<QuantifyEvolve>builder()
                    .dataList(dataList)
                    .entityClass(QuantifyEvolve.class)
                    .fileName("量化值进化量")
                    .sheetName("量化值进化量")
                    .summaryRows(summaryRows)
                    .fieldMapping(fieldMapping)
                    .build();

            // 使用通用导出方法
            UniversalExcelExporter.exportExcel(response, config);

        } catch (Exception e) {
            log.error("导出量化值进化量异常", e);
        }
    }
}
