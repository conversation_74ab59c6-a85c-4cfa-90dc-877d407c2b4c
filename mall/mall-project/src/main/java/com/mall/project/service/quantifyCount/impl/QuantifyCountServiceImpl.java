package com.mall.project.service.quantifyCount.impl;

import com.mall.common.api.CommonPage;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.quantifyCount.QuantifyCountDao;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.quantifyCount.QuantifyCountService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 量化数计算服务实现类
 */
@Service
@Slf4j
public class QuantifyCountServiceImpl implements QuantifyCountService {

    @Autowired
    private QuantifyCountDao quantifyCountDao;


    @Override
    public Map<String, Object> getQuantifyCount() {
        return quantifyCountDao.getQuantifyCount();
    }

    /**
     * 每天的量化数计算
     */
    public void updateQuantifyCount() {
        quantifyCountDao.updateQuantifyCount();
    }

    @Override
    public Map<String, Object> saveOrUpdateQuantifyCount(String isEnabled, String proportion, Integer updatePerson) {
        if(isEnabled == null || isEnabled.isEmpty() || !isEnabled.matches("^[01]$")) {
            throw new BusinessException("量化数设置开、关不能为空，且只能为0或1");
        }
        if(proportion != null && proportion.isEmpty() && !proportion.matches("^\\d+(\\.\\d{1,4})?$")) {
            throw new BusinessException("每日每ID的分量进化量化数只能为正整数或小数，且最多保留4位小数");
        }
        int  result = quantifyCountDao.saveOrUpdateQuantifyCount(isEnabled, proportion, updatePerson);
        if(result > 0){
            return getQuantifyCount();
        }else{
            return null;
        }
    }

    @Override
    public CommonPage<Map<String, Object>> queryQuantifyCountPages(String phone, String startTime, String endTime, int pageNum, int pageSize) {
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }
        int offset = (pageNum - 1) * pageSize;
        //验证开始日期格式 yyyy-MM-dd
        if(startTime != null && !startTime.isEmpty() && !startTime.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        //验证结束日期格式 yyyy-MM-dd
        if(endTime != null && !endTime.isEmpty() && !endTime.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 验证pageNum 不能小于1
        if (pageNum < 1) {
            throw new BusinessException("pageNum不能小于1");
        }
        // 验证pageSize 不能小于1
        if (pageSize < 1) {
            throw new BusinessException("pageSize不能小于1");
        }
        List<Map<String, Object>> list = quantifyCountDao.quantifyCount(phone,  startTime, endTime, pageSize, offset);
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedTradeDataSet = list.stream().map(ConvertToCamelCase::convertToCamelCase).toList();

        long total = quantifyCountDao.totalQuantifyCount(phone, startTime, endTime);

        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);

        QuantifyCountServiceImpl.CustomCommonPage<Map<String, Object>> commonPage = new QuantifyCountServiceImpl.CustomCommonPage<>(pageNum, pageSize, totalPages, total, formattedTradeDataSet);

        // 添加汇总数据到返回结果中
        Map<String, Object> summary = new HashMap<>();
        summary.put("todayTotalQuan", ConvertToCamelCase.formatDecimal(new BigDecimal(todayTotalQuan(phone, startTime))));   //今日总量化数
        summary.put("weightCountTotal", ConvertToCamelCase.formatDecimal(new BigDecimal(weightCountTotal(phone, startTime))));   //累计总量化数
        // Admin的每日累计量化数
        summary.put("adminDailyQuantity", ConvertToCamelCase.formatDecimal(new BigDecimal(getAdminDailyQuantity(startTime))));
        // Admin的累计量化数
        summary.put("adminTotalQuantity", ConvertToCamelCase.formatDecimal(new BigDecimal(adminTotalQuantity(startTime))));
        // 中南惠C的每日所有ID累计量化数
        summary.put("cweightCountTotal", ConvertToCamelCase.formatDecimal(new BigDecimal(cweightCountTotal(startTime))));
        // 中南惠B的每日所有ID累计量化数
        summary.put("bweightCountTotal", ConvertToCamelCase.formatDecimal(new BigDecimal(bweightCountTotal(startTime))));
        // 中南惠C的所有ID累计量化数
        summary.put("cweightCountTotalAllDays", ConvertToCamelCase.formatDecimal(new BigDecimal(cweightCountTotalAllDays(startTime))));
        // 中南惠B的所有ID累计量化数
        summary.put("bweightCountTotalAlldays", ConvertToCamelCase.formatDecimal(new BigDecimal(bweightCountTotalAlldays(startTime))));
        // 中南惠系统的每日总累计量化数
        summary.put("sumWeightCountTotal", ConvertToCamelCase.formatDecimal(new BigDecimal(sumWeightCountTotal(startTime))));
        // Admin的累计量化数 + 中南惠C的所有ID累计量化数 + 中南惠B的所有ID累计量化数 的总和
        summary.put("sumWeightCountTotalAllDays", ConvertToCamelCase.formatDecimal(new BigDecimal(sumWeightCountTotalAllDays(startTime))));
        summary.put("todayTotalWeight", ConvertToCamelCase.formatDecimal(new BigDecimal(todayTotalWeight(phone, startTime))));   //今日总分量
        summary.put("totalWeight", ConvertToCamelCase.formatDecimal(new BigDecimal(totalWeight(phone, startTime))));   //今日累计总分量
        commonPage.setSummary(summary);

        return commonPage;
    }
    /**
     * 今日总量化数
     */
    public String todayTotalQuan(String phone, String startTime){
        return quantifyCountDao.todaytotalQuantify(phone,  startTime) == null ? "0" : quantifyCountDao.todaytotalQuantify(phone,  startTime);
    }

    /**
     * 今日累计量化数
     */
    public String weightCountTotal(String phone, String startTime){
        return quantifyCountDao.weightCountTotal(phone,  startTime) == null ? "0" : quantifyCountDao.weightCountTotal(phone,  startTime);
    }

    // 新增内部类扩展CommonPage
    @Setter
    @Getter
    private static class CustomCommonPage<T> extends CommonPage<T> {
        private Map<String, Object> summary;
        public CustomCommonPage(int pageNum, int pageSize, int totalPage, long total, List<T> list) {
            super(pageNum, pageSize, totalPage, total, list);
        }
    }

    /**
     * 查看 所有合作企业Admain的各ID每日每笔数据量化数比 得出 Admin的每日累计量化数
     */
    @Override
    public String getAdminDailyQuantity(String startTime) {
        return quantifyCountDao.getAdminDailyQuantity(startTime);
    }

    /**
     * 中南惠C的每日所有ID累计量化数
     */
    @Override
    public String cweightCountTotal(String startTime) {
        String result = quantifyCountDao.cweightCountTotal(startTime);
        return result == null ? "0" : result;
    }

    /**
     * 中南惠B的每日所有ID累计量化数
     */
    @Override
    public String bweightCountTotal(String startTime) {
        String result = quantifyCountDao.bweightCountTotal(startTime);
        return result == null ? "0" : result;
    }

    @Override
    public String sumWeightCountTotal(String startTime) {
        String adminDailyQuantitynum =  getAdminDailyQuantity(startTime);  //Admin的每日累计量化数
        String cweightCountTotal = cweightCountTotal(startTime);
        String bweightCountTotal = bweightCountTotal(startTime);
        return new BigDecimal(adminDailyQuantitynum).add(new BigDecimal(cweightCountTotal)).add(new BigDecimal(bweightCountTotal)).setScale(2, BigDecimal.ROUND_DOWN).toString();
    }

    /**
     *  查询量化数设置 导出 Excel
     */
    @Override
    public List<Map<String, Object>> exportQuantifyCountExcel(String phone, String startTime, String endTime) {
        List<Map<String, Object>> list = quantifyCountDao.exportQuantifyCountExcel(startTime, endTime);
        // 转换下划线格式为驼峰格式
        return list.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
    }

    /**
     * Admin的累计量化数
     */
    @Override
    public String adminTotalQuantity(String startTime) {
        String result = quantifyCountDao.adminTotalQuantity(startTime);
        return result == null ? "0" : result;
    }

    /**
     * 计算 中南惠C的所有ID累计量化数
     */
    @Override
    public String cweightCountTotalAllDays(String startTime) {
        String result = quantifyCountDao.cweightCountTotalAllDays(startTime);
        return result == null ? "0" : result;
    }

    /**
     * 计算 中南惠B的所有ID累计量化数
     */
    @Override
    public String bweightCountTotalAlldays(String startTime) {
        String result = quantifyCountDao.bweightCountTotalAlldays(startTime);
        return result == null ? "0" : result;
    }

    /**
     * 计算Admin的累计量化数 + 中南惠C的所有ID累计量化数 + 中南惠B的所有ID累计量化数 的总和
     */
    @Override
    public String sumWeightCountTotalAllDays(String startTime) {
        String adminTotalQuantity = adminTotalQuantity(startTime);
        String cweightCountTotalAllDays = cweightCountTotalAllDays(startTime);
        String bweightCountTotalAlldays = bweightCountTotalAlldays(startTime);
        return new BigDecimal(adminTotalQuantity).add(new BigDecimal(cweightCountTotalAllDays)).add(new BigDecimal(bweightCountTotalAlldays)).toString();
    }
    /**
     * 今日总分量
     */
    @Override
    public String todayTotalWeight(String phone, String startTime) {
        return quantifyCountDao.todayTotalWeight(phone, startTime) == null ? "0" : quantifyCountDao.todayTotalWeight(phone, startTime);
    }
    /**
     * 今日累计总分量
     */
    @Override
    public String totalWeight(String phone, String startTime) {
        return quantifyCountDao.totalWeight(phone, startTime) == null ? "0" : quantifyCountDao.totalWeight(phone, startTime);
    }
    /**
     * 更新 累计量化数
     */
    @Override
    public void updateTotalQuantifyCount() {
        quantifyCountDao.updateTotalQuantifyCount();
    }
}
