package com.mall.project.service.adminQuantifyValue;

import com.mall.common.api.CommonPage;

import java.util.List;
import java.util.Map;

/**
 * Admin 量化值接口
 */
public interface AdminQuantifyValueService {

    /**
     * 查询Admin量化值, 分页显示
     */
    public CommonPage<Map<String, Object>> queryAdminAdminQuantifyValuePages(String phone, String startDate, String endDate, int pageNum, int pageSize);

    /**
     * 今日Admin量化值
     */
    public String todayAdminAdminQuantifyValue(String phone,String startTime);

    /**
     * 累计Admin量化值
     */
    public String totalAdminAdminQuantifyValue(String startDate);


    /**
     * 导出Admin量化值 Excel
     */
    public List<Map<String, Object>> exportAdminAdminQuantifyValueExcel(String phone,String startDate, String endDate);
}
