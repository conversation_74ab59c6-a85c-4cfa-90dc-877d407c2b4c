package com.mall.project.dao.quantizationValue;

import com.mall.project.service.areaAuthorize.AreaAuthorizeService;
import com.mall.project.service.bcSettings.BCSettingsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.mall.common.util.StringUtils.isNotEmpty;

/**
 * 量化值数据访问对象
 */
@Repository
public class QuantizationValueDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private BCSettingsService bcSettingsService;

    @Autowired
    private AreaAuthorizeService areaAuthorizeService;

    /**
     * 量化值计算
     */
    public void updateQuantizationValue() {
        String sql = "SELECT c.phone,u.user_type,c.quantify_count,u.deduction_money_limit,u.flag,u.town_code FROM mall_b_users u,mall_b_users_count c\n" +
                "WHERE u.phone = c.phone \n" +
                "AND u.`status` = 0 \n" +
                "AND DATE(c.update_time) = CURDATE() ";
        List<Map<String, Object>> list = jdbcTemplate.queryForList(sql);
        //查询C设置
        Map<String, Object> cSettings = bcSettingsService.getCSettings();
        // 合作企业各IDC设置 的开关
        String isEnabled = cSettings.get("isEnabled").toString();
        // C每ID每日自动量化值进化信用值%
        String quantifyToCredit = cSettings.get("quantifyToCredit").toString();
        // C每ID每日自动信用值进化平台补贴金%
        String creditToCoupon = cSettings.get("creditToCoupon").toString();
        // 今天的量化率
        BigDecimal quantizationRate = new BigDecimal(getLatestQuantizationRate(""));
        for (Map<String, Object> map : list) {
            String phone = (String) map.get("phone");
            Object townCodeObj = map.get("town_code");
            if (" ".equals(townCodeObj.toString()) || "null".equals(townCodeObj.toString()) || townCodeObj.toString() == null) {
                continue; // 跳过town_code为null的记录
            }
            String areaId = townCodeObj.toString();
            
            //平台兑换金上限
            Object deductionMoneyLimitObj = map.get("deduction_money_limit");
            if (deductionMoneyLimitObj == null) {
                continue; // 跳过deduction_money_limit为null的记录
            }
            BigDecimal deductionMoneyLimit = new BigDecimal(deductionMoneyLimitObj.toString());
            
            Object quantifyCountObj = map.get("quantify_count");   //累计量化数
            if (quantifyCountObj == null) {
                continue; // 跳过quantify_count为null的记录
            }
            BigDecimal quantifyCount = new BigDecimal(quantifyCountObj.toString());

            // 计算基础量化值
            BigDecimal quantizationValue = quantifyCount.multiply(quantizationRate).setScale(2, BigDecimal.ROUND_DOWN);

            // 计算总量化值
            BigDecimal totalQuantizationValue = quantizationValue.add(new BigDecimal(sumQuantizationValue(phone))).setScale(2, BigDecimal.ROUND_DOWN);

            String userType = (String) map.get("user_type");
            BigDecimal creditValue = new BigDecimal("0");
            BigDecimal totalCreditValue = new BigDecimal("0");
            if("C".equals(userType)){
                // 计算信用值
                 creditValue = quantizationValue.multiply(new BigDecimal(quantifyToCredit)).setScale(2, BigDecimal.ROUND_DOWN).divide(new BigDecimal(100), 2, BigDecimal.ROUND_DOWN);
                // 计算总信用值
                totalCreditValue = creditValue.add(new BigDecimal(sumCreditValue(phone))).setScale(2, BigDecimal.ROUND_DOWN);
            }

            // 计算平台补贴金
            BigDecimal platformGold = new BigDecimal("0");
            // 计算今天的平台补贴金
            BigDecimal countResult = creditValue.multiply(new BigDecimal(creditToCoupon)).setScale(2, BigDecimal.ROUND_DOWN).divide(new BigDecimal(100), 2, BigDecimal.ROUND_DOWN);

            BigDecimal totalPlatformGold = new BigDecimal("0");
            // 流失平台金
            BigDecimal lostPlatformGold = new BigDecimal("0");
            Object flagObj = map.get("flag");
            if (flagObj == null) {
                continue; // 跳过flag为null的记录
            }
            if(flagObj.toString().equals("0")){     //判断用户达标状态:0未达标,1达标1,2达标2
                platformGold = new BigDecimal("0");
                totalPlatformGold = new BigDecimal(sumPlatformGold(phone)).setScale(2, BigDecimal.ROUND_DOWN);
                lostPlatformGold = countResult;

                //将流失的平台金记录到 授权结果表中
                // 获取用户所在区域的完整层级路径（省、市、县、乡镇）
                String givephone = areaAuthorizeService.findAuthorizedPhoneFromHightToLow(areaId);
                // 如果找到了手机号，进行后续处理
                if(isNotEmpty(givephone)) {   // 未达标 所有的分量全部流失
                    //areaAuthorizeService.updateCSubsidy(givephone, lostPlatformGold);
                }
            }else{  //达标用户
                //如果平台补贴金超出上限，则将平台补贴金设置为等于上限
                if(countResult.compareTo(deductionMoneyLimit) > 0){   //超出上限
                    platformGold = deductionMoneyLimit;
                    // 计算总平台补贴金
                    totalPlatformGold = deductionMoneyLimit.add(new BigDecimal(sumPlatformGold(phone))).setScale(2, BigDecimal.ROUND_DOWN);
                    lostPlatformGold = countResult.subtract(deductionMoneyLimit);

                    //乡镇、县、市、省 一级一级往上找代理的手机号,将超出上限的平台补贴金给到代理人
                    String givePhone = areaAuthorizeService.findAuthorizedPhoneFromLowToHigh(areaId);
                    // 如果找到了手机号，进行后续处理
                    if(isNotEmpty(givePhone)){  //达标用户只流失超出上限的部分

                    }
                }else{  // 未超出上限
                    platformGold = countResult;
                    // 计算总平台补贴金
                    totalPlatformGold = countResult.add(new BigDecimal(sumPlatformGold(phone))).setScale(2, BigDecimal.ROUND_DOWN);
                }
            }
            // 更新量化值  quantization_value ,如果CURDATE() - INTERVAL 1 DAY 的数据不存在则插入, 否则更新,使用 SELECT EXISTS 判断
            if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM quantization_value WHERE phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, phone) == 0) {
                if("C".equals(userType)){
                    if (isEnabled.equals("0")) {
                        sql = "INSERT INTO quantization_value(phone,value,total_value,credit_value,total_credit_value,count_result,platform_gold,total_platform_gold,update_date)VALUES(?,?,?,?,?,?,?,?,CURDATE() )";
                        jdbcTemplate.update(sql, phone, quantizationValue, totalQuantizationValue, creditValue, totalCreditValue, countResult, platformGold, totalPlatformGold);
                    }
                }else{
                    sql = "INSERT INTO quantization_value(phone,value,total_value,credit_value,total_credit_value,count_result,platform_gold,total_platform_gold,update_date)VALUES(?,?,?,?,?,?,?,?,CURDATE() )";
                    jdbcTemplate.update(sql, phone, quantizationValue, totalQuantizationValue, creditValue, totalCreditValue, countResult, platformGold, totalPlatformGold);
                }
            }else{
                if("C".equals(userType)){
                    if (isEnabled.equals("0")) {
                        sql = "UPDATE quantization_value SET value = ?,total_value = ?,credit_value = ?,total_credit_value = ?,count_result = ?,platform_gold = ?,total_platform_gold = ? WHERE phone = ? AND DATE(update_date) = CURDATE() ";
                        jdbcTemplate.update(sql, quantizationValue, totalQuantizationValue, creditValue, totalCreditValue, countResult, platformGold, totalPlatformGold, phone);
                    }
                }else{
                    sql = "UPDATE quantization_value SET value = ?,total_value = ?,credit_value = ?,total_credit_value = ?,count_result = ?,platform_gold = ?,total_platform_gold = ? WHERE phone = ? AND DATE(update_date) = CURDATE() ";
                    jdbcTemplate.update(sql, quantizationValue, totalQuantizationValue, creditValue, totalCreditValue, countResult, platformGold, totalPlatformGold, phone);
                }
            }
        }
    }

    /**
     * 查询最新日期的量化率
     */
    public String getLatestQuantizationRate(String startTime) {
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(quantify_rate, '0') as quantify_rate FROM quantization_rate WHERE 1=1";
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND DATE(quantify_date) = ?";
                params.add(startTime);
            }else{
                sql += " AND DATE(quantify_date) = CURDATE() ";
            }
            String result;
            if(params.isEmpty()){
                result = jdbcTemplate.queryForObject(sql, String.class);
            }else{
                result = jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
            return result != null ? result : "0";
        } catch (Exception e) {
            return "0";
        }
    }
    /**
     * 统计 platform_gold ,phone 的和
     */
    public String sumPlatformGold(String phone) {
        try{
            String sql = "SELECT SUM(platform_gold) FROM quantization_value WHERE phone = ? AND DATE(update_date) < CURDATE() ";
            String sum = jdbcTemplate.queryForObject(sql, String.class, phone);
            return sum == null ? "0" : sum;
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 统计 quantization_value ,phone 的和
     */
    public String sumQuantizationValue(String phone) {
        try{
            String sql = "SELECT SUM(value) FROM quantization_value WHERE phone = ? AND DATE(update_date) < CURDATE() ";
            String sum = jdbcTemplate.queryForObject(sql, String.class, phone);
            return sum == null ? "0" : sum;
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 统计 credit_value ,phone 的和
     */
    public String sumCreditValue(String phone) {
        try{
            String sql = "SELECT SUM(credit_value) FROM quantization_value WHERE phone = ? AND DATE(update_date) < CURDATE() ";
            String sum = jdbcTemplate.queryForObject(sql, String.class, phone);
            return sum == null ? "0" : sum;
        } catch (Exception e) {
            return "0";
        }
    }


    /**
     * 查询量化值, 分页显示
     */
    public List<Map<String, Object>> queryQuantizationValuePages(String phone, String startDate, String endDate, int limit, int offset) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT v.update_date,v.phone,u.username,v.`value`,v.total_value,v.credit_Value,v.total_credit_Value,v.platform_gold,v.total_platform_gold FROM quantization_value v LEFT JOIN mall_b_users u ON v.phone = u.phone WHERE 1=1";
        if (isNotEmpty(phone)) {
            sql += " AND v.phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (isNotEmpty(startDate)) {
            sql += " AND DATE(v.update_date) = ?";
            params.add(startDate);
        }else{
            sql += " AND DATE(v.update_date) <= CURDATE() ";
        }
        sql += " ORDER BY v.update_date DESC,v.id DESC LIMIT " + limit + " OFFSET " + offset;
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }

    /**
     * 量化值总条数
     */
    public int totalQuantizationValue(String phone, String startDate, String endDate) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COUNT(1) FROM quantization_value v LEFT JOIN mall_b_users u ON v.phone = u.phone WHERE 1=1";
        if (isNotEmpty(phone)) {
            sql += " AND v.phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (isNotEmpty(startDate)) {
            sql += " AND DATE(v.update_date) = ?";
            params.add(startDate);
        }else{
            sql += " AND DATE(v.update_date) <= CURDATE() ";
        }
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, Integer.class);
        }else{
            return jdbcTemplate.queryForObject(sql, Integer.class, params.toArray());
        }
    }

    /**
     * 导出量化值 Excel
     */
    public List<Map<String, Object>> exportQuantizationValueExcel(String phone, String startDate, String endDate) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT v.update_date,v.phone,u.username,v.`value`,v.total_value,v.credit_Value,v.total_credit_Value,v.platform_gold,v.total_platform_gold FROM quantization_value v LEFT JOIN mall_b_users u ON v.phone = u.phone WHERE 1=1";
        if (isNotEmpty(phone)) {
            sql += " AND v.phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (isNotEmpty(startDate)) {
            sql += " AND DATE(v.update_date) = ?";
            params.add(startDate);
        }else{
            sql += " AND DATE(v.update_date) <= CURDATE() ";
        }
        sql += " ORDER BY v.update_date DESC";
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }

    /**
     * 每日平台补贴金, 只有用户类型为C的才有
     */
    public String todayTotalPlatformGold(String phone, String startTime){
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(sum(v.platform_gold), 0) as today_total_platform_gold FROM quantization_value v, mall_b_users u\n" +
                    "where v.phone = u.phone\n" +
                    "and u.user_type = 'C' ";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND v.phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND DATE(v.update_date) = ?";
                params.add(startTime);
            }else{
                sql += " AND DATE(v.update_date) = CURDATE() ";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 累计平台补贴金
     */
    public String totalPlatformGold(String phone, String startTime){
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(sum(v.total_platform_gold), 0) AS today_total_platform_gold\n" +
                    "FROM (\n" +
                    "    SELECT \n" +
                    "        v.total_platform_gold,\n" +
                    "        ROW_NUMBER() OVER (\n" +
                    "            PARTITION BY v.phone \n" +
                    "            ORDER BY v.update_date DESC, v.id DESC\n" +
                    "        ) AS rn\n" +
                    "    FROM quantization_value v, mall_b_users u\n" +
                    "where v.phone = u.phone\n" +
                    "and u.user_type = 'C' ";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND v.phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND DATE(v.update_date) = ?";
                params.add(startTime);
            }else{
                sql += " AND DATE(v.update_date) = CURDATE() ";
            }
            sql += " ) AS ranked WHERE rn = 1";
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 每日平台促销金, 只有用户类型为B的才有
     */
    public String todayTotalPromotionGold(String phone, String startTime){
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(sum(v.platform_gold), 0) as today_total_platform_gold FROM quantization_value v, mall_b_users u\n" +
                    "where v.phone = u.phone\n" +
                    "and u.user_type = 'B' ";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND v.phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND DATE(v.update_date) = ?";
                params.add(startTime);
            }else{
                sql += " AND DATE(v.update_date) = CURDATE() ";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 累计平台促销金
     */
    public String totalPromotionGold(String phone, String startTime){
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(sum(v.total_platform_gold), 0) AS today_total_platform_gold\n" +
                    "FROM (\n" +
                    "    SELECT \n" +
                    "        v.total_platform_gold,\n" +
                    "        ROW_NUMBER() OVER (\n" +
                    "            PARTITION BY v.phone \n" +
                    "            ORDER BY v.update_date DESC, v.id DESC\n" +
                    "        ) AS rn\n" +
                    "    FROM quantization_value v, mall_b_users u\n" +
                    "where v.phone = u.phone\n" +
                    "and u.user_type = 'B' ";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND v.phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND DATE(v.update_date) = ?";
                params.add(startTime);
            }else{
                sql += " AND DATE(v.update_date) = CURDATE() ";
            }
            sql += " ) AS ranked WHERE rn = 1";
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 查询B,CB的每日量化值,并推送到mallB系统 admin 的分开推,在这个方法下面
     */
    public List<Map<String, Object>> queryTodayQuantizationValue() {
        try {
            String sql = "SELECT v.phone,v.VALUE AS quantificationValue FROM quantization_value v,mall_b_users u WHERE v.phone = u.phone AND DATE (v.update_date ) = CURDATE() - INTERVAL 1 DAY AND (u.user_type = 'B' or u.user_type = 'CB')";
            return jdbcTemplate.queryForList(sql);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 查询Admin的每日量化值,并推送到mallB系统
     */
    public String queryTodayAdminQuantizationValue() {
        try {
            String sql = "SELECT v.phone,v.VALUE AS quantificationValue FROM quantization_value v,mall_b_users u WHERE v.phone = u.phone AND DATE (v.update_date ) = CURDATE() - INTERVAL 1 DAY AND u.username = 'admin'";
            return jdbcTemplate.queryForObject(sql, String.class);
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 查询每日平台补贴金,并推送到mallB系统
     */
    public List<Map<String, Object>> queryTodayPlatformGold() {
        try {
            String sql = "SELECT v.phone,v.platform_gold as amount FROM quantization_value v,mall_b_users u WHERE v.phone = u.phone AND DATE (v.update_date ) = CURDATE() - INTERVAL 1 DAY AND u.user_type = 'C' AND v.platform_gold > 0";
            return jdbcTemplate.queryForList(sql);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 每日量化值累计
     */
    public String everydayTotalQuantizationValue(String phone, String startTime) {
        String phoneSql = "SELECT phone FROM mall_b_users WHERE username = 'admin'";
        String adminPhone = jdbcTemplate.queryForObject(phoneSql, String.class);
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(sum(value), 0) as everyday_total_value FROM quantization_value WHERE phone <> ?";
            params.add(adminPhone);
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND DATE(update_date) = ?";
                params.add(startTime);
            }else{
                sql += " AND DATE(update_date) = CURDATE() ";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 总量化值累计
     */
    public String allTotalQuantizationValue(String phone, String startTime) {
        String phoneSql = "SELECT phone FROM mall_b_users WHERE username = 'admin'";
        String adminPhone = jdbcTemplate.queryForObject(phoneSql, String.class);
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(sum(total_value), 0) AS all_total_value\n" +
                    "FROM (\n" +
                    "    SELECT \n" +
                    "        total_value,\n" +
                    "        ROW_NUMBER() OVER (\n" +
                    "            PARTITION BY phone \n" +
                    "            ORDER BY update_date DESC, id DESC\n" +
                    "        ) AS rn\n" +
                    "    FROM quantization_value WHERE phone <> ?";
            params.add(adminPhone);
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND DATE(update_date) = ?";
                params.add(startTime);
            }else{
                sql += " AND DATE(update_date) = CURDATE() ";
            }
            sql += " ) AS ranked WHERE rn = 1";
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 每日量化累计
     */
    public String everydayTotalCreditValue(String phone, String startTime) {
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(sum(credit_Value), 0) as everyday_total_credit_Value FROM quantization_value WHERE 1=1";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND DATE(update_date) = ?";
                params.add(startTime);
            }else{
                sql += " AND DATE(update_date) = CURDATE() ";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 总量化累计
     */
    public String allTotalCreditValue(String phone, String startTime) {
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(sum(total_credit_Value), 0) AS all_total_credit_Value\n" +
                    "FROM (\n" +
                    "    SELECT \n" +
                    "        total_credit_Value,\n" +
                    "        ROW_NUMBER() OVER (\n" +
                    "            PARTITION BY phone \n" +
                    "            ORDER BY update_date DESC, id DESC\n" +
                    "        ) AS rn\n" +
                    "    FROM quantization_value WHERE 1=1";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND DATE(update_date) = ?";
                params.add(startTime);
            }else{
                sql += " AND DATE(update_date) = CURDATE() ";
            }
            sql += " ) AS ranked WHERE rn = 1";
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            return "0";
        }
    }

    public void saveAdminQuantifyValue(String value){
        // 查询 mall_b_users 表 username = 'admin' 的 手机号
        String phoneSql = "SELECT phone FROM mall_b_users WHERE username = 'admin'";
        String phone = jdbcTemplate.queryForObject(phoneSql, String.class);
        // 查看 quantization_value 表 当天是否有数据,如果没有则插入一条数据
        String checkQuantizationValueSql = "SELECT COUNT(*) FROM quantization_value WHERE phone = ? AND DATE(update_date) = CURDATE() ";
        Integer quantizationValueCount = jdbcTemplate.queryForObject(checkQuantizationValueSql, Integer.class,phone);
        if (quantizationValueCount == null || quantizationValueCount <= 0) {
            String insertQuantizationValueSql = "INSERT INTO quantization_value(phone,value,total_value,update_date) VALUES(?,?,?,CURDATE() )";
            jdbcTemplate.update(insertQuantizationValueSql, phone,value,new BigDecimal(value).add(new BigDecimal(sumQuantizationValue(phone))));
        }else{
            String updateQuantizationValueSql = "UPDATE quantization_value SET value = ?,total_value = ? WHERE phone = ? AND DATE(update_date) = CURDATE() ";
            jdbcTemplate.update(updateQuantizationValueSql, value,new BigDecimal(value).add(new BigDecimal(sumQuantizationValue(phone))),phone);
        }
    }

    /**
     * 累计Admin量化值
     */
    public String totalAdminAdminQuantifyValue(String startDate){
        List<Object> params = new ArrayList<>();
        String phoneSql = "SELECT phone FROM mall_b_users WHERE username = 'admin'";
        String phone = jdbcTemplate.queryForObject(phoneSql, String.class);
        String sql = "select total_value from quantization_value WHERE phone = ?";
        params.add(phone);
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND DATE(update_date) = ?";
            params.add(startDate);
        }else{
            sql += " AND DATE(update_date) = CURDATE() ";
        }
        try {
            String result;
            if(params.isEmpty()){
                result = jdbcTemplate.queryForObject(sql, String.class);
            }else{
                result = jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
            return result != null ? result : "0";
        } catch (Exception e) {
            return "0";
        }
    }
}
