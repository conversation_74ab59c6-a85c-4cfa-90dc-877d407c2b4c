package com.mall.project.service.mallBUsers.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mall.common.api.CommonPage;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.mallBUsers.GetMallBUsersDao;
import com.mall.project.dto.mallBUsers.MallBUsers;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.mallBUsers.GetMallBUsersService;
import com.mall.project.util.MallBAuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class GetMallBUsersServiceImpl implements GetMallBUsersService {

    @Autowired
    private MallBAuthUtils mallBAuthUtils;

    @Autowired
    private GetMallBUsersDao getMallBUsersDao;

    /**
     * 从mallB系统读取用户数据
     */
    @Override
    @Transactional
    public void getMallBUsers() {
        try {
            // 创建ObjectMapper用于JSON处理
            ObjectMapper objectMapper = new ObjectMapper();

            // 使用工具类发送带参数的GET请求到mallB系统获取交易数据
            ResponseEntity<String> dealDataResponse = mallBAuthUtils.getForEntity("/mall/receptionA/getRelationChain", String.class);

            // 检查获取交易数据是否成功
            if (dealDataResponse.getStatusCode() != HttpStatus.OK) {
                throw new BusinessException("获取mallB用户数据数据失败: " + dealDataResponse.getStatusCode());
            }

            // 解析交易数据响应
            String dealDataResponseBody = dealDataResponse.getBody();
            if (dealDataResponseBody == null) {
                throw new BusinessException("mallB用户数据为空");
            }

            // 解析响应JSON
            JsonNode dealDataRoot = objectMapper.readTree(dealDataResponseBody);

            if (dealDataRoot.get("code").asInt() != 200) {
                throw new BusinessException("获取mallB用户数据失败: " + dealDataRoot.get("msg").asText());
            }

            // 获取mallB用户数据列表
            JsonNode userDataList = dealDataRoot.path("data");
            if (!userDataList.isArray() || userDataList.isEmpty()) {
                log.info("mallB用户数据为空");
                return;
            }
            // 存储交易数据到本地系统
            log.info("开始处理并存储mallB用户数据，共 {} 条记录", userDataList.size());
            // 删除所有mallB系统的用户数据
            getMallBUsersDao.deleteMallBUsers();

            // 遍历交易数据并存储
            for (JsonNode userItem : userDataList) {
                // 把 userItem 的值都打印出来
                try {
                    MallBUsers mallBUsers = new MallBUsers();
                    mallBUsers.setId(userItem.path("id").asText());
                    mallBUsers.setUserName(userItem.path("username").asText());
                    mallBUsers.setParentId(Objects.equals(userItem.path("parentId").asText(), "null") ? " " : userItem.path("parentId").asText());
                    mallBUsers.setParentUserName(Objects.equals(userItem.path("parentNusername").asText(), "null") ? " " : userItem.path("parentNusername").asText());
                    mallBUsers.setUserType(userItem.path("userType").asText());
                    mallBUsers.setChainType(userItem.path("chainType").asText());
                    mallBUsers.setStatus(Objects.equals(userItem.path("status").asText(), "null") ? "0" : userItem.path("status").asText());
                    mallBUsers.setDeductionMoneyLimit(Objects.equals(userItem.path("deductionMoneyLimit").asText(), "null") ? "0" : userItem.path("deductionMoneyLimit").asText());
                    mallBUsers.setBusinessName(Objects.equals(userItem.path("businessName").asText(), "null") ? " " : userItem.path("businessName").asText());
                    mallBUsers.setPhone(Objects.equals(userItem.path("phone").asText(), "null") ? " " : userItem.path("phone").asText());
                    mallBUsers.setFans(Integer.parseInt(Objects.equals(userItem.path("fans").asText(), "null") ? "0" : userItem.path("fans").asText()));
                    // 拼接地址
                    String province = Objects.equals(userItem.path("province").asText(), "null") ? " " : userItem.path("province").asText();
                    String city = Objects.equals(userItem.path("city").asText(), "null") ? " " : userItem.path("city").asText();
                    String district = Objects.equals(userItem.path("district").asText(), "null") ? " " : userItem.path("district").asText();
                    String town = Objects.equals(userItem.path("town").asText(), "null") ? " " : userItem.path("town").asText();
                    String address = Objects.equals(userItem.path("address").asText(), "null") ? " " : userItem.path("address").asText();
                    String fullAddress = province + city + district + town + address;
                    mallBUsers.setAddress(fullAddress);
                    mallBUsers.setTownCode(Objects.equals(userItem.path("townCode").asText(), "null") ? " " : userItem.path("townCode").asText());
                    mallBUsers.setJurisdiction(Objects.equals(userItem.path("jurisdiction").asText(), "null") ? "0" : userItem.path("jurisdiction").asText());
                    mallBUsers.setFlag(Objects.equals(userItem.path("flag").asText(), "null") ? "0" : userItem.path("flag").asText());
                    mallBUsers.setLoginAddress(Objects.equals(userItem.path("loginAddress").asText(), "null") ? " " : userItem.path("loginAddress").asText());
                    mallBUsers.setCreateTime(Objects.equals(userItem.path("createTime").asText(), "null") ? " " : userItem.path("createTime").asText());
                    mallBUsers.setTodayWeight(Objects.equals(userItem.path("todayComponent").asText(), "null") ? "0" : userItem.path("todayComponent").asText());
                    mallBUsers.setWeightCount(Objects.equals(userItem.path("component").asText(), "null") ? "0" : userItem.path("component").asText());
                    mallBUsers.setSocialCreditCode(Objects.equals(userItem.path("socialCreditCode").asText(), "null") ? " " : userItem.path("socialCreditCode").asText());

                    // 调用DAO层存储数据
                    getMallBUsersDao.saveMallBUsers(mallBUsers);
                    //查询 用户禁用状态记录表(disable_status) status = 1 的用户,如果有则把用户的status更新为1
                    List<Map<String, Object>> disableStatusList = getMallBUsersDao.queryDisableStatus();
                    if (disableStatusList != null && !disableStatusList.isEmpty()) {
                        for (Map<String, Object> disableStatus : disableStatusList) {
                            getMallBUsersDao.updateMallBUsersStatus(disableStatus.get("user_id").toString(), "1");
                        }
                    }
                } catch (Exception e) {
                    log.error("处理mallB交易数据项失败: {}", e.getMessage(), e);
                    return;
                }
            }
            log.info("成功完成mallB用户数据同步");

        } catch (Exception e) {
            log.error("与mallB系统通信失败: {}", e.getMessage(), e);
        }
    }
    // 从mallB系统读取超级管理员手机号码
    @Override
    public void getSuperAdminPhone() {
        try {
            // 创建ObjectMapper用于JSON处理
            ObjectMapper objectMapper = new ObjectMapper();

            // 使用工具类发送带参数的GET请求到mallB系统获取超级管理员手机号码
            ResponseEntity<String> dealDataResponse = mallBAuthUtils.getForEntity("/mall/receptionA/getAdminPhone", String.class);

            // 检查获取超级管理员手机号码是否成功
            if (dealDataResponse.getStatusCode() != HttpStatus.OK) {
                throw new BusinessException("获取mallB超级管理员手机号码失败: " + dealDataResponse.getStatusCode());
            }

            // 解析超级管理员手机号码响应
            String dealDataResponseBody = dealDataResponse.getBody();
            if (dealDataResponseBody == null) {
                throw new BusinessException("mallB超级管理员手机号码为空");
            }

            // 解析响应JSON
            JsonNode root = objectMapper.readTree(dealDataResponseBody);

            if (root.get("code").asInt() != 200) {
                throw new BusinessException("获取mallB超级管理员手机号码失败: " + root.get("msg").asText());
            }

            // 获取超级管理员手机号码
            JsonNode dataList = root.path("data");
            if (dataList == null) {
                log.info("mallB超级管理员手机号码为空");
                return;
            }
            // 调用DAO层存储数据
            getMallBUsersDao.saveSuperAdminPhone(dataList.asText());
            log.info("成功完成mallB超级管理员手机号码同步");

        } catch (Exception e) {
            log.error("与mallB系统通信失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public CommonPage<Map<String, Object>> getMallBUsers(String phone, String businessLicense, int pageNum, int pageSize) {
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }
        int offset = (pageNum - 1) * pageSize;
        List<Map<String, Object>> dataList = getMallBUsersDao.getMallBUsers(phone,businessLicense, pageSize, offset);
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedTradeDataSet = dataList.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
        long total = getMallBUsersDao.getMallBUsersCount(phone,businessLicense);
        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);
        // 转换下划线格式为驼峰格式
        return new CommonPage<>(pageNum, pageSize, totalPages, total, formattedTradeDataSet);
    }

    // 更新用户状态
    @Override
    public int updateMallBUsersStatus(String id, String status) {
        // 参数验证
        if (id == null || id.isEmpty()) {
            throw new BusinessException("用户ID不能为空");
        }
        if (status == null || status.isEmpty()) {
            throw new BusinessException("用户状态不能为空");
        }
        //用户状态（0正常 1禁用 3 失效 4 睡眠 5 无效）
        if (!status.equals("0") && !status.equals("1") && !status.equals("3") && !status.equals("4") && !status.equals("5")) {
            throw new BusinessException("用户状态参数错误");
        }
        if (status.equals("1")) {  //禁用用户,插入到用户禁用状态记录表(disable_status)
            getMallBUsersDao.insertDisableStatus(id, status);
        }else{ // 启用用户,删除用户禁用状态记录表(disable_status)
            getMallBUsersDao.deleteDisableStatus(id);
        }
        return getMallBUsersDao.updateMallBUsersStatus(id, status);
    }
}
